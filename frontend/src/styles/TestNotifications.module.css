.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  min-height: calc(100vh - 60px);
  padding-top: 80px;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.header p {
  color: #65676b;
  font-size: 1.1rem;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section h2 {
  color: #1a1a1a;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.section ol,
.section ul {
  margin: 0;
  padding-left: 1.5rem;
}

.section li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
  color: #65676b;
}

.section li:last-child {
  margin-bottom: 0;
}

.actions {
  text-align: center;
  padding: 2rem 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
    padding-top: 80px;
  }
  
  .section {
    padding: 1rem;
  }
  
  .section h2 {
    font-size: 1.2rem;
  }
}
