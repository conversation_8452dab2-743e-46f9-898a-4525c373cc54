'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { userAPI } from '@/utils/api';
import { useAlert } from '@/contexts/AlertContext';
import Button from '@/components/Button';
import ProtectedRoute from '@/components/ProtectedRoute';
import styles from '@/styles/TestNotifications.module.css';

export default function TestNotifications() {
  const { user } = useAuth();
  const { showAlert } = useAlert();
  const [isLoading, setIsLoading] = useState(false);

  const createTestFollowRequest = async () => {
    setIsLoading(true);
    try {
      // This would normally be done by another user, but for testing we'll simulate it
      showAlert({
        type: 'info',
        title: 'Test Notification',
        message: 'In a real scenario, another user would send you a follow request. Check your notification dropdown!'
      });
    } catch (error) {
      console.error('Error creating test notification:', error);
      showAlert({
        type: 'error',
        title: 'Error',
        message: 'Failed to create test notification'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1>Test Notification Dropdown</h1>
          <p>This page helps test the notification dropdown functionality.</p>
        </div>

        <div className={styles.content}>
          <div className={styles.section}>
            <h2>How to Test the Notification Dropdown</h2>
            <ol>
              <li>Look at the navbar - you should see a "Notifications" button instead of a link</li>
              <li>Click on the "Notifications" button to open the dropdown</li>
              <li>The dropdown should show recent notifications with quick action buttons</li>
              <li>For follow requests and group invites, you should see Accept/Decline buttons</li>
              <li>Click "View All Notifications" to go to the full notifications page</li>
              <li>Click outside the dropdown to close it</li>
            </ol>
          </div>

          <div className={styles.section}>
            <h2>Features Implemented</h2>
            <ul>
              <li>✅ Notification dropdown in navbar</li>
              <li>✅ Shows recent notifications (up to 8)</li>
              <li>✅ Quick action buttons for follow requests</li>
              <li>✅ Quick action buttons for group invites</li>
              <li>✅ Quick action buttons for group join requests</li>
              <li>✅ "View All" link to notifications page</li>
              <li>✅ Click outside to close dropdown</li>
              <li>✅ Real-time updates via WebSocket</li>
              <li>✅ Responsive design for mobile</li>
              <li>✅ Unread notification indicators</li>
            </ul>
          </div>

          <div className={styles.section}>
            <h2>Test Instructions</h2>
            <p>To fully test the notification dropdown:</p>
            <ol>
              <li>Have another user send you a follow request</li>
              <li>Have someone invite you to a group</li>
              <li>Create a group and have someone request to join</li>
              <li>Check that notifications appear in real-time</li>
              <li>Test the quick action buttons work correctly</li>
              <li>Verify the dropdown closes after actions</li>
            </ol>
          </div>

          <div className={styles.actions}>
            <Button
              variant="primary"
              onClick={createTestFollowRequest}
              disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Show Test Info'}
            </Button>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
